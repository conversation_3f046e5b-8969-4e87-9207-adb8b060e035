"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Home/SpectacularHero.jsx":
/*!*************************************************!*\
  !*** ./src/components/Home/SpectacularHero.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst SpectacularHero = ()=>{\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Simple scroll tracking\n    const [titleOpacity, setTitleOpacity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [titleY, setTitleY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SpectacularHero.useEffect\": ()=>{\n            setIsLoaded(true);\n            // Scroll listener for parallax\n            const handleScroll = {\n                \"SpectacularHero.useEffect.handleScroll\": ()=>{\n                    const scrolled = window.scrollY;\n                    setScrollY(scrolled);\n                    setTitleOpacity(Math.max(0, 1 - scrolled / 300));\n                    setTitleY(-scrolled / 3);\n                }\n            }[\"SpectacularHero.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            // Initialize fireflies canvas\n            initFireflies();\n            return ({\n                \"SpectacularHero.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"SpectacularHero.useEffect\"];\n        }\n    }[\"SpectacularHero.useEffect\"], []);\n    // Fireflies animation\n    const initFireflies = ()=>{\n        if (false) {}\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext('2d');\n        canvas.width = window.innerWidth;\n        canvas.height = window.innerHeight;\n        const fireflies = [];\n        const numFireflies = 15;\n        // Create fireflies\n        for(let i = 0; i < numFireflies; i++){\n            fireflies.push({\n                x: Math.random() * canvas.width,\n                y: Math.random() * canvas.height,\n                vx: (Math.random() - 0.5) * 0.5,\n                vy: (Math.random() - 0.5) * 0.5,\n                opacity: Math.random(),\n                opacityDirection: Math.random() > 0.5 ? 1 : -1,\n                size: Math.random() * 3 + 1\n            });\n        }\n        // Animation loop\n        const animate = ()=>{\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            fireflies.forEach((firefly)=>{\n                // Update position\n                firefly.x += firefly.vx;\n                firefly.y += firefly.vy;\n                // Bounce off edges\n                if (firefly.x < 0 || firefly.x > canvas.width) firefly.vx *= -1;\n                if (firefly.y < 0 || firefly.y > canvas.height) firefly.vy *= -1;\n                // Update opacity\n                firefly.opacity += firefly.opacityDirection * 0.01;\n                if (firefly.opacity <= 0 || firefly.opacity >= 1) {\n                    firefly.opacityDirection *= -1;\n                }\n                // Draw firefly\n                ctx.beginPath();\n                ctx.arc(firefly.x, firefly.y, firefly.size, 0, Math.PI * 2);\n                ctx.fillStyle = `rgba(255, 215, 0, ${firefly.opacity * 0.6})`;\n                ctx.fill();\n                // Glow effect\n                ctx.beginPath();\n                ctx.arc(firefly.x, firefly.y, firefly.size * 3, 0, Math.PI * 2);\n                ctx.fillStyle = `rgba(255, 215, 0, ${firefly.opacity * 0.1})`;\n                ctx.fill();\n            });\n            requestAnimationFrame(animate);\n        };\n        animate();\n    };\n    // Letter reveal animation with CSS\n    const getLetterStyle = (index)=>({\n            opacity: isMounted && isLoaded ? 1 : 0,\n            transform: isMounted && isLoaded ? 'translateY(0) rotateX(0) scale(1)' : 'translateY(50px) rotateX(90deg) scale(0.8)',\n            transition: `all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) ${index * 0.1}s`\n        });\n    const title = \"BAKASANA\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: heroRef,\n        className: \"relative h-screen overflow-hidden\",\n        style: {\n            height: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-10\",\n                style: {\n                    transform: isMounted ? `translateY(${scrollY * 0.05}px)` : 'translateY(0px)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full\",\n                    style: {\n                        background: `\n              linear-gradient(\n                180deg,\n                rgba(255, 183, 77, 0.3) 0%,\n                rgba(255, 138, 101, 0.4) 25%,\n                rgba(255, 107, 107, 0.3) 50%,\n                rgba(196, 113, 237, 0.2) 75%,\n                rgba(72, 187, 120, 0.1) 100%\n              )\n            `,\n                        backgroundSize: '400% 400%',\n                        animation: 'gradientShift 15s ease infinite'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-20\",\n                style: {\n                    transform: isMounted ? `translateY(${scrollY * 0.1}px)` : 'translateY(0px)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cloud cloud-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cloud cloud-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cloud cloud-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 z-30 h-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"wave wave-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"wave wave-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"wave wave-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 z-40 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-50 flex flex-col items-center justify-center text-center\",\n                style: {\n                    opacity: isMounted ? titleOpacity : 1,\n                    transform: isMounted ? `translateY(${titleY}px)` : 'translateY(0px)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center\",\n                                children: title.split('').map((letter, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block font-cormorant font-light text-white\",\n                                        style: {\n                                            ...getLetterStyle(i),\n                                            fontSize: 'clamp(80px, 12vw, 120px)',\n                                            letterSpacing: '0.2em',\n                                            textShadow: `\n                    0 0 20px rgba(255, 255, 255, 0.5),\n                    0 0 40px rgba(255, 215, 0, 0.3),\n                    0 0 60px rgba(255, 138, 101, 0.2)\n                  `,\n                                            WebkitTextStroke: '2px transparent',\n                                            background: 'linear-gradient(135deg, #FFD700 0%, #FF8A65 50%, #FF6B6B 100%)',\n                                            WebkitBackgroundClip: 'text',\n                                            WebkitTextFillColor: 'transparent',\n                                            filter: 'drop-shadow(0 0 10px rgba(255, 215, 0, 0.4))'\n                                        },\n                                        children: letter\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 pointer-events-none\",\n                                style: {\n                                    background: 'radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, transparent 70%)',\n                                    filter: 'blur(20px)',\n                                    animation: 'pulse 3s ease-in-out infinite'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/90 font-cormorant italic font-light tracking-wide\",\n                        style: {\n                            fontSize: 'clamp(16px, 3vw, 18px)',\n                            textShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n                            opacity: isLoaded ? 1 : 0,\n                            transform: isLoaded ? 'translateY(0)' : 'translateY(30px)',\n                            transition: 'all 0.8s ease 2s',\n                            animation: isLoaded ? 'breathe 4s ease-in-out infinite' : 'none'\n                        },\n                        children: \"~ j\\xf3ga jest drogą ciszy ~\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-50\",\n                style: {\n                    opacity: isLoaded ? 1 : 0,\n                    transform: `translateX(-50%) translateY(${isLoaded ? 0 : 20}px)`,\n                    transition: 'all 0.8s ease 3s'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-white/70 cursor-pointer hover:text-white transition-colors duration-300\",\n                    style: {\n                        animation: 'float 3s ease-in-out infinite'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"40\",\n                            height: \"40\",\n                            viewBox: \"0 0 40 40\",\n                            fill: \"currentColor\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M20 5c-3.5 0-6.5 2.5-7.5 6-1 3.5 0 7 2.5 9.5s6 2.5 9.5 1.5 6-4 6-7.5-2.5-6.5-6-7.5c-1-.3-2-.5-3-.5zm0 2c2.5 0 4.5 2 4.5 4.5s-2 4.5-4.5 4.5-4.5-2-4.5-4.5 2-4.5 4.5-4.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M15 20c-2 0-3.5 1.5-3.5 3.5s1.5 3.5 3.5 3.5 3.5-1.5 3.5-3.5-1.5-3.5-3.5-3.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M25 20c-2 0-3.5 1.5-3.5 3.5s1.5 3.5 3.5 3.5 3.5-1.5 3.5-3.5-1.5-3.5-3.5-3.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M20 30c-1.5 0-2.5 1-2.5 2.5s1 2.5 2.5 2.5 2.5-1 2.5-2.5-1-2.5-2.5-2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs mt-2 tracking-wider\",\n                            children: \"SCROLL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SpectacularHero, \"kKPd3uoqDZu0PBmP4UWDHaAQCXI=\");\n_c = SpectacularHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SpectacularHero);\nvar _c;\n$RefreshReg$(_c, \"SpectacularHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Home/SpectacularHero.jsx\n"));

/***/ })

});