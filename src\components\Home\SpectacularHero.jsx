'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';

const SpectacularHero = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const heroRef = useRef(null);
  const canvasRef = useRef(null);
  
  // Simple scroll tracking
  const [titleOpacity, setTitleOpacity] = useState(1);
  const [titleY, setTitleY] = useState(0);

  useEffect(() => {
    setIsLoaded(true);

    // Scroll listener for parallax
    const handleScroll = () => {
      const scrolled = window.scrollY;
      setScrollY(scrolled);
      setTitleOpacity(Math.max(0, 1 - scrolled / 300));
      setTitleY(-scrolled / 3);
    };

    window.addEventListener('scroll', handleScroll);

    // Initialize fireflies canvas
    initFireflies();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Fireflies animation
  const initFireflies = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    
    const fireflies = [];
    const numFireflies = 15;
    
    // Create fireflies
    for (let i = 0; i < numFireflies; i++) {
      fireflies.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        opacity: Math.random(),
        opacityDirection: Math.random() > 0.5 ? 1 : -1,
        size: Math.random() * 3 + 1
      });
    }
    
    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      fireflies.forEach(firefly => {
        // Update position
        firefly.x += firefly.vx;
        firefly.y += firefly.vy;
        
        // Bounce off edges
        if (firefly.x < 0 || firefly.x > canvas.width) firefly.vx *= -1;
        if (firefly.y < 0 || firefly.y > canvas.height) firefly.vy *= -1;
        
        // Update opacity
        firefly.opacity += firefly.opacityDirection * 0.01;
        if (firefly.opacity <= 0 || firefly.opacity >= 1) {
          firefly.opacityDirection *= -1;
        }
        
        // Draw firefly
        ctx.beginPath();
        ctx.arc(firefly.x, firefly.y, firefly.size, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 215, 0, ${firefly.opacity * 0.6})`;
        ctx.fill();
        
        // Glow effect
        ctx.beginPath();
        ctx.arc(firefly.x, firefly.y, firefly.size * 3, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 215, 0, ${firefly.opacity * 0.1})`;
        ctx.fill();
      });
      
      requestAnimationFrame(animate);
    };
    
    animate();
  };

  // Letter reveal animation with CSS
  const getLetterStyle = (index) => ({
    opacity: isLoaded ? 1 : 0,
    transform: isLoaded ? 'translateY(0) rotateX(0) scale(1)' : 'translateY(50px) rotateX(90deg) scale(0.8)',
    transition: `all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) ${index * 0.1}s`
  });

  const title = "BAKASANA";

  return (
    <section 
      ref={heroRef}
      className="relative h-screen overflow-hidden"
      style={{ height: '100vh' }}
    >
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/background/bali-hero.webp"
          alt="BAKASANA - Retreaty jogi Bali i Sri Lanka"
          fill
          className="object-cover object-center"
          priority
          sizes="100vw"
          quality={95}
        />

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/20 to-black/40" />
        <div className="absolute inset-0 bg-sanctuary/10" />
      </div>



      {/* Layer 4: Fireflies Canvas */}
      <canvas 
        ref={canvasRef}
        className="absolute inset-0 z-40 pointer-events-none"
      />

      {/* Main Content */}
      <div
        className="absolute inset-0 z-50 flex flex-col items-center justify-center text-center"
        style={{
          opacity: titleOpacity,
          transform: `translateY(${titleY}px)`
        }}
      >
        {/* Main Title with Letter Animation */}
        <div className="mb-8 relative">
          <div className="flex justify-center items-center">
            {title.split('').map((letter, i) => (
              <span
                key={i}
                className="inline-block font-cormorant font-light text-white"
                style={{
                  ...getLetterStyle(i),
                  fontSize: 'clamp(80px, 12vw, 120px)',
                  letterSpacing: '0.2em',
                  textShadow: `
                    0 0 20px rgba(255, 255, 255, 0.5),
                    0 0 40px rgba(255, 215, 0, 0.3),
                    0 0 60px rgba(255, 138, 101, 0.2)
                  `,
                  WebkitTextStroke: '2px transparent',
                  background: 'linear-gradient(135deg, #FFD700 0%, #FF8A65 50%, #FF6B6B 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  filter: 'drop-shadow(0 0 10px rgba(255, 215, 0, 0.4))'
                }}
              >
                {letter}
              </span>
            ))}
          </div>

          {/* Pulsing Glow Effect */}
          <div
            className="absolute inset-0 pointer-events-none"
            style={{
              background: 'radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, transparent 70%)',
              filter: 'blur(20px)',
              animation: 'pulse 3s ease-in-out infinite'
            }}
          />
        </div>

        {/* Subtitle with Breathing Animation */}
        <p
          className="text-white/90 font-cormorant italic font-light tracking-wide"
          style={{
            fontSize: 'clamp(16px, 3vw, 18px)',
            textShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
            opacity: isLoaded ? 1 : 0,
            transform: isLoaded ? 'translateY(0)' : 'translateY(30px)',
            transition: 'all 0.8s ease 2s',
            animation: isLoaded ? 'breathe 4s ease-in-out infinite' : 'none'
          }}
        >
          ~ jóga jest drogą ciszy ~
        </p>
      </div>

      {/* Animated Lotus Scroll Indicator */}
      <div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-50"
        style={{
          opacity: isLoaded ? 1 : 0,
          transform: `translateX(-50%) translateY(${isLoaded ? 0 : 20}px)`,
          transition: 'all 0.8s ease 3s'
        }}
      >
        <div
          className="text-white/70 cursor-pointer hover:text-white transition-colors duration-300"
          style={{
            animation: 'float 3s ease-in-out infinite'
          }}
        >
          <svg width="40" height="40" viewBox="0 0 40 40" fill="currentColor">
            <path d="M20 5c-3.5 0-6.5 2.5-7.5 6-1 3.5 0 7 2.5 9.5s6 2.5 9.5 1.5 6-4 6-7.5-2.5-6.5-6-7.5c-1-.3-2-.5-3-.5zm0 2c2.5 0 4.5 2 4.5 4.5s-2 4.5-4.5 4.5-4.5-2-4.5-4.5 2-4.5 4.5-4.5z"/>
            <path d="M15 20c-2 0-3.5 1.5-3.5 3.5s1.5 3.5 3.5 3.5 3.5-1.5 3.5-3.5-1.5-3.5-3.5-3.5z"/>
            <path d="M25 20c-2 0-3.5 1.5-3.5 3.5s1.5 3.5 3.5 3.5 3.5-1.5 3.5-3.5-1.5-3.5-3.5-3.5z"/>
            <path d="M20 30c-1.5 0-2.5 1-2.5 2.5s1 2.5 2.5 2.5 2.5-1 2.5-2.5-1-2.5-2.5-2.5z"/>
          </svg>
          <div className="text-xs mt-2 tracking-wider">SCROLL</div>
        </div>
      </div>
    </section>
  );
};

export default SpectacularHero;
