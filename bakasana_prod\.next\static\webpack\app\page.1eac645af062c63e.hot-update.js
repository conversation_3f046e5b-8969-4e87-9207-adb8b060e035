"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Home/SpectacularHero.jsx":
/*!*************************************************!*\
  !*** ./src/components/Home/SpectacularHero.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst SpectacularHero = ()=>{\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Simple scroll tracking\n    const [titleOpacity, setTitleOpacity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [titleY, setTitleY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SpectacularHero.useEffect\": ()=>{\n            setIsLoaded(true);\n            // Scroll listener for parallax\n            const handleScroll = {\n                \"SpectacularHero.useEffect.handleScroll\": ()=>{\n                    const scrolled = window.scrollY;\n                    setScrollY(scrolled);\n                    setTitleOpacity(Math.max(0, 1 - scrolled / 300));\n                    setTitleY(-scrolled / 3);\n                }\n            }[\"SpectacularHero.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            // Initialize fireflies canvas\n            initFireflies();\n            return ({\n                \"SpectacularHero.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"SpectacularHero.useEffect\"];\n        }\n    }[\"SpectacularHero.useEffect\"], []);\n    // Fireflies animation\n    const initFireflies = ()=>{\n        if (false) {}\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext('2d');\n        canvas.width = window.innerWidth;\n        canvas.height = window.innerHeight;\n        const fireflies = [];\n        const numFireflies = 15;\n        // Create fireflies\n        for(let i = 0; i < numFireflies; i++){\n            fireflies.push({\n                x: Math.random() * canvas.width,\n                y: Math.random() * canvas.height,\n                vx: (Math.random() - 0.5) * 0.5,\n                vy: (Math.random() - 0.5) * 0.5,\n                opacity: Math.random(),\n                opacityDirection: Math.random() > 0.5 ? 1 : -1,\n                size: Math.random() * 3 + 1\n            });\n        }\n        // Animation loop\n        const animate = ()=>{\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            fireflies.forEach((firefly)=>{\n                // Update position\n                firefly.x += firefly.vx;\n                firefly.y += firefly.vy;\n                // Bounce off edges\n                if (firefly.x < 0 || firefly.x > canvas.width) firefly.vx *= -1;\n                if (firefly.y < 0 || firefly.y > canvas.height) firefly.vy *= -1;\n                // Update opacity\n                firefly.opacity += firefly.opacityDirection * 0.01;\n                if (firefly.opacity <= 0 || firefly.opacity >= 1) {\n                    firefly.opacityDirection *= -1;\n                }\n                // Draw firefly\n                ctx.beginPath();\n                ctx.arc(firefly.x, firefly.y, firefly.size, 0, Math.PI * 2);\n                ctx.fillStyle = `rgba(255, 215, 0, ${firefly.opacity * 0.6})`;\n                ctx.fill();\n                // Glow effect\n                ctx.beginPath();\n                ctx.arc(firefly.x, firefly.y, firefly.size * 3, 0, Math.PI * 2);\n                ctx.fillStyle = `rgba(255, 215, 0, ${firefly.opacity * 0.1})`;\n                ctx.fill();\n            });\n            requestAnimationFrame(animate);\n        };\n        animate();\n    };\n    // Letter reveal animation with CSS\n    const getLetterStyle = (index)=>({\n            opacity: isLoaded ? 1 : 0,\n            transform: isLoaded ? 'translateY(0) rotateX(0) scale(1)' : 'translateY(50px) rotateX(90deg) scale(0.8)',\n            transition: `all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) ${index * 0.1}s`\n        });\n    const title = \"BAKASANA\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: heroRef,\n        className: \"relative h-screen overflow-hidden\",\n        style: {\n            height: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-10\",\n                style: {\n                    transform: `translateY(${scrollY * 0.05}px)`\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full\",\n                    style: {\n                        background: `\n              linear-gradient(\n                180deg,\n                rgba(255, 183, 77, 0.3) 0%,\n                rgba(255, 138, 101, 0.4) 25%,\n                rgba(255, 107, 107, 0.3) 50%,\n                rgba(196, 113, 237, 0.2) 75%,\n                rgba(72, 187, 120, 0.1) 100%\n              )\n            `,\n                        backgroundSize: '400% 400%',\n                        animation: 'gradientShift 15s ease infinite'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-20\",\n                style: {\n                    transform: isMounted ? `translateY(${scrollY * 0.1}px)` : 'translateY(0px)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cloud cloud-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cloud cloud-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cloud cloud-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 z-30 h-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"wave wave-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"wave wave-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"wave wave-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 z-40 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-50 flex flex-col items-center justify-center text-center\",\n                style: {\n                    opacity: isMounted ? titleOpacity : 1,\n                    transform: isMounted ? `translateY(${titleY}px)` : 'translateY(0px)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center\",\n                                children: title.split('').map((letter, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block font-cormorant font-light text-white\",\n                                        style: {\n                                            ...getLetterStyle(i),\n                                            fontSize: 'clamp(80px, 12vw, 120px)',\n                                            letterSpacing: '0.2em',\n                                            textShadow: `\n                    0 0 20px rgba(255, 255, 255, 0.5),\n                    0 0 40px rgba(255, 215, 0, 0.3),\n                    0 0 60px rgba(255, 138, 101, 0.2)\n                  `,\n                                            WebkitTextStroke: '2px transparent',\n                                            background: 'linear-gradient(135deg, #FFD700 0%, #FF8A65 50%, #FF6B6B 100%)',\n                                            WebkitBackgroundClip: 'text',\n                                            WebkitTextFillColor: 'transparent',\n                                            filter: 'drop-shadow(0 0 10px rgba(255, 215, 0, 0.4))'\n                                        },\n                                        children: letter\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 pointer-events-none\",\n                                style: {\n                                    background: 'radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, transparent 70%)',\n                                    filter: 'blur(20px)',\n                                    animation: 'pulse 3s ease-in-out infinite'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/90 font-cormorant italic font-light tracking-wide\",\n                        style: {\n                            fontSize: 'clamp(16px, 3vw, 18px)',\n                            textShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n                            opacity: isLoaded ? 1 : 0,\n                            transform: isLoaded ? 'translateY(0)' : 'translateY(30px)',\n                            transition: 'all 0.8s ease 2s',\n                            animation: isLoaded ? 'breathe 4s ease-in-out infinite' : 'none'\n                        },\n                        children: \"~ j\\xf3ga jest drogą ciszy ~\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-50\",\n                style: {\n                    opacity: isLoaded ? 1 : 0,\n                    transform: `translateX(-50%) translateY(${isLoaded ? 0 : 20}px)`,\n                    transition: 'all 0.8s ease 3s'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-white/70 cursor-pointer hover:text-white transition-colors duration-300\",\n                    style: {\n                        animation: 'float 3s ease-in-out infinite'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"40\",\n                            height: \"40\",\n                            viewBox: \"0 0 40 40\",\n                            fill: \"currentColor\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M20 5c-3.5 0-6.5 2.5-7.5 6-1 3.5 0 7 2.5 9.5s6 2.5 9.5 1.5 6-4 6-7.5-2.5-6.5-6-7.5c-1-.3-2-.5-3-.5zm0 2c2.5 0 4.5 2 4.5 4.5s-2 4.5-4.5 4.5-4.5-2-4.5-4.5 2-4.5 4.5-4.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M15 20c-2 0-3.5 1.5-3.5 3.5s1.5 3.5 3.5 3.5 3.5-1.5 3.5-3.5-1.5-3.5-3.5-3.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M25 20c-2 0-3.5 1.5-3.5 3.5s1.5 3.5 3.5 3.5 3.5-1.5 3.5-3.5-1.5-3.5-3.5-3.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M20 30c-1.5 0-2.5 1-2.5 2.5s1 2.5 2.5 2.5 2.5-1 2.5-2.5-1-2.5-2.5-2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs mt-2 tracking-wider\",\n                            children: \"SCROLL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SpectacularHero, \"kKPd3uoqDZu0PBmP4UWDHaAQCXI=\");\n_c = SpectacularHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SpectacularHero);\nvar _c;\n$RefreshReg$(_c, \"SpectacularHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Home/SpectacularHero.jsx\n"));

/***/ })

});