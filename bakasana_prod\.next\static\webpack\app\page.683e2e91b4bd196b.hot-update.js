"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Home/SpectacularHero.jsx":
/*!*************************************************!*\
  !*** ./src/components/Home/SpectacularHero.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst SpectacularHero = ()=>{\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Simple scroll tracking\n    const [titleOpacity, setTitleOpacity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [titleY, setTitleY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SpectacularHero.useEffect\": ()=>{\n            setIsLoaded(true);\n            // Scroll listener for parallax\n            const handleScroll = {\n                \"SpectacularHero.useEffect.handleScroll\": ()=>{\n                    const scrolled = window.scrollY;\n                    setScrollY(scrolled);\n                    setTitleOpacity(Math.max(0, 1 - scrolled / 300));\n                    setTitleY(-scrolled / 3);\n                }\n            }[\"SpectacularHero.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            // Initialize fireflies canvas\n            initFireflies();\n            return ({\n                \"SpectacularHero.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"SpectacularHero.useEffect\"];\n        }\n    }[\"SpectacularHero.useEffect\"], []);\n    // Fireflies animation\n    const initFireflies = ()=>{\n        if (false) {}\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext('2d');\n        canvas.width = window.innerWidth;\n        canvas.height = window.innerHeight;\n        const fireflies = [];\n        const numFireflies = 15;\n        // Create fireflies\n        for(let i = 0; i < numFireflies; i++){\n            fireflies.push({\n                x: Math.random() * canvas.width,\n                y: Math.random() * canvas.height,\n                vx: (Math.random() - 0.5) * 0.5,\n                vy: (Math.random() - 0.5) * 0.5,\n                opacity: Math.random(),\n                opacityDirection: Math.random() > 0.5 ? 1 : -1,\n                size: Math.random() * 3 + 1\n            });\n        }\n        // Animation loop\n        const animate = ()=>{\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            fireflies.forEach((firefly)=>{\n                // Update position\n                firefly.x += firefly.vx;\n                firefly.y += firefly.vy;\n                // Bounce off edges\n                if (firefly.x < 0 || firefly.x > canvas.width) firefly.vx *= -1;\n                if (firefly.y < 0 || firefly.y > canvas.height) firefly.vy *= -1;\n                // Update opacity\n                firefly.opacity += firefly.opacityDirection * 0.01;\n                if (firefly.opacity <= 0 || firefly.opacity >= 1) {\n                    firefly.opacityDirection *= -1;\n                }\n                // Draw firefly\n                ctx.beginPath();\n                ctx.arc(firefly.x, firefly.y, firefly.size, 0, Math.PI * 2);\n                ctx.fillStyle = `rgba(255, 215, 0, ${firefly.opacity * 0.6})`;\n                ctx.fill();\n                // Glow effect\n                ctx.beginPath();\n                ctx.arc(firefly.x, firefly.y, firefly.size * 3, 0, Math.PI * 2);\n                ctx.fillStyle = `rgba(255, 215, 0, ${firefly.opacity * 0.1})`;\n                ctx.fill();\n            });\n            requestAnimationFrame(animate);\n        };\n        animate();\n    };\n    // Letter reveal animation with CSS\n    const getLetterStyle = (index)=>({\n            opacity: isLoaded ? 1 : 0,\n            transform: isLoaded ? 'translateY(0) rotateX(0) scale(1)' : 'translateY(50px) rotateX(90deg) scale(0.8)',\n            transition: `all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) ${index * 0.1}s`\n        });\n    const title = \"BAKASANA\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: heroRef,\n        className: \"relative h-screen overflow-hidden\",\n        style: {\n            height: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-10\",\n                style: {\n                    transform: isMounted ? `translateY(${scrollY * 0.05}px)` : 'translateY(0px)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full\",\n                    style: {\n                        background: `\n              linear-gradient(\n                180deg,\n                rgba(255, 183, 77, 0.3) 0%,\n                rgba(255, 138, 101, 0.4) 25%,\n                rgba(255, 107, 107, 0.3) 50%,\n                rgba(196, 113, 237, 0.2) 75%,\n                rgba(72, 187, 120, 0.1) 100%\n              )\n            `,\n                        backgroundSize: '400% 400%',\n                        animation: 'gradientShift 15s ease infinite'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-20\",\n                style: {\n                    transform: isMounted ? `translateY(${scrollY * 0.1}px)` : 'translateY(0px)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cloud cloud-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cloud cloud-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cloud cloud-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 z-30 h-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"wave wave-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"wave wave-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"wave wave-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 z-40 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-50 flex flex-col items-center justify-center text-center\",\n                style: {\n                    opacity: isMounted ? titleOpacity : 1,\n                    transform: isMounted ? `translateY(${titleY}px)` : 'translateY(0px)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center\",\n                                children: title.split('').map((letter, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block font-cormorant font-light text-white\",\n                                        style: {\n                                            ...getLetterStyle(i),\n                                            fontSize: 'clamp(80px, 12vw, 120px)',\n                                            letterSpacing: '0.2em',\n                                            textShadow: `\n                    0 0 20px rgba(255, 255, 255, 0.5),\n                    0 0 40px rgba(255, 215, 0, 0.3),\n                    0 0 60px rgba(255, 138, 101, 0.2)\n                  `,\n                                            WebkitTextStroke: '2px transparent',\n                                            background: 'linear-gradient(135deg, #FFD700 0%, #FF8A65 50%, #FF6B6B 100%)',\n                                            WebkitBackgroundClip: 'text',\n                                            WebkitTextFillColor: 'transparent',\n                                            filter: 'drop-shadow(0 0 10px rgba(255, 215, 0, 0.4))'\n                                        },\n                                        children: letter\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 pointer-events-none\",\n                                style: {\n                                    background: 'radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, transparent 70%)',\n                                    filter: 'blur(20px)',\n                                    animation: 'pulse 3s ease-in-out infinite'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/90 font-cormorant italic font-light tracking-wide\",\n                        style: {\n                            fontSize: 'clamp(16px, 3vw, 18px)',\n                            textShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n                            opacity: isLoaded ? 1 : 0,\n                            transform: isLoaded ? 'translateY(0)' : 'translateY(30px)',\n                            transition: 'all 0.8s ease 2s',\n                            animation: isLoaded ? 'breathe 4s ease-in-out infinite' : 'none'\n                        },\n                        children: \"~ j\\xf3ga jest drogą ciszy ~\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-50\",\n                style: {\n                    opacity: isLoaded ? 1 : 0,\n                    transform: `translateX(-50%) translateY(${isLoaded ? 0 : 20}px)`,\n                    transition: 'all 0.8s ease 3s'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-white/70 cursor-pointer hover:text-white transition-colors duration-300\",\n                    style: {\n                        animation: 'float 3s ease-in-out infinite'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"40\",\n                            height: \"40\",\n                            viewBox: \"0 0 40 40\",\n                            fill: \"currentColor\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M20 5c-3.5 0-6.5 2.5-7.5 6-1 3.5 0 7 2.5 9.5s6 2.5 9.5 1.5 6-4 6-7.5-2.5-6.5-6-7.5c-1-.3-2-.5-3-.5zm0 2c2.5 0 4.5 2 4.5 4.5s-2 4.5-4.5 4.5-4.5-2-4.5-4.5 2-4.5 4.5-4.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M15 20c-2 0-3.5 1.5-3.5 3.5s1.5 3.5 3.5 3.5 3.5-1.5 3.5-3.5-1.5-3.5-3.5-3.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M25 20c-2 0-3.5 1.5-3.5 3.5s1.5 3.5 3.5 3.5 3.5-1.5 3.5-3.5-1.5-3.5-3.5-3.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M20 30c-1.5 0-2.5 1-2.5 2.5s1 2.5 2.5 2.5 2.5-1 2.5-2.5-1-2.5-2.5-2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs mt-2 tracking-wider\",\n                            children: \"SCROLL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\Home\\\\SpectacularHero.jsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SpectacularHero, \"kKPd3uoqDZu0PBmP4UWDHaAQCXI=\");\n_c = SpectacularHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SpectacularHero);\nvar _c;\n$RefreshReg$(_c, \"SpectacularHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Home/SpectacularHero.jsx\n"));

/***/ })

});