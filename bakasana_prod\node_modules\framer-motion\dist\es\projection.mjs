export { frame, frameData, mix, recordStats, statsBuffer } from 'motion-dom';
export { calcBoxDelta } from './projection/geometry/delta-calc.mjs';
export { nodeGroup } from './projection/node/group.mjs';
export { HTMLProjectionNode } from './projection/node/HTMLProjectionNode.mjs';
export { correctBorderRadius } from './projection/styles/scale-border-radius.mjs';
export { correctBoxShadow } from './projection/styles/scale-box-shadow.mjs';
export { addScaleCorrector } from './projection/styles/scale-correction.mjs';
export { HTMLVisualElement } from './render/html/HTMLVisualElement.mjs';
export { buildTransform } from './render/html/utils/build-transform.mjs';
